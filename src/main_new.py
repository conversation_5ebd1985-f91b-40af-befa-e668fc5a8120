"""
Spark MCP Server - Self-Evolved Memory with Two-Phase Architecture

Implements the evidence-based two-phase memory pipeline from Mem0 research
with BGE embeddings, PostgreSQL+pgvector, and OpenMemory MCP interface.
"""

from mcp.server.fastmcp import FastMCP, Context
from contextlib import asynccontextmanager
from collections.abc import Async<PERSON><PERSON><PERSON>
from dataclasses import dataclass
from dotenv import load_dotenv
from typing import Optional, List
import asyncio
import json
import os
import logging
import threading
import time
from datetime import datetime
from pathlib import Path


# Import our modules
from bge_embedding_client import BGEEmbeddingClient
from memory_database import MemoryDatabase
from memory_extraction import MemoryExtractionModule
from memory_update import MemoryUpdateModule
from rolling_summary import RollingSummaryManager
from performance_monitor import PerformanceMonitor
from simple_llm_client import get_spark_memory_client
from supabase_mcp_integration import RealSupabaseMCP
from llm_cache_service import initialize_llm_cache
from session_manager import SessionManager
from graph_memory_service import GraphMemoryService

# Import new refactored modules
from config import get_config
from server_readiness import get_readiness_manager
from exceptions import SparkMemoryError, create_error_response, handle_known_exception
from memory_handlers import (
    get_memory_system_context,
    process_memory_pipeline,
    create_context_access_error_response,
    create_memory_operation_error_response
)
# Import enhanced search modules
from enhanced_search import EnhancedMemorySearch
from enhanced_search_handler import (
    search_memory_enhanced,
    parse_search_filters,
    search_by_entity_handler
)
# Import session management
from session_manager import SessionManager

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv()

# Initialize configuration
config = get_config()

def get_server_user_id() -> str:
    """
    Get server-controlled user ID - no client override allowed.

    Uses configuration-based user ID resolution with priority order:
    1. Environment variable MCP_USER_ID (primary)
    2. PROJECT_ID with "project:" prefix
    3. TEAM_ID with "team:" prefix
    4. Default fallback "user"

    Returns:
        Server-controlled user ID string
    """
    user_sources = config.get_user_id_sources()

    # Primary: Use MCP_USER_ID if explicitly set
    if user_sources["primary"]:
        return user_sources["primary"]

    # Check for project-based ID from environment
    project_id = os.getenv("PROJECT_ID")
    if project_id:
        return f"{user_sources['project_prefix']}{project_id}"

    # Check for team-based ID from environment
    team_id = os.getenv("TEAM_ID")
    if team_id:
        base = f"{user_sources['team_prefix']}{team_id}"
        if project_id:
            return f"{base}:{user_sources['project_prefix']}{project_id}"
        return base

    # Default fallback
    return user_sources["default"]


def get_user_id(ctx: Context) -> str:
    """
    Get user ID from MCP context.

    For now, this delegates to get_server_user_id() for consistency.
    In the future, this could extract user information from the context.

    Args:
        ctx: MCP context (currently unused but kept for API compatibility)

    Returns:
        User ID string
    """
    return get_server_user_id()

# Get global readiness manager from server_readiness module
readiness_manager = get_readiness_manager()

@dataclass
class MemorySystemContext:
    """Context for the Spark memory system with two-phase architecture."""
    extraction: MemoryExtractionModule
    update: MemoryUpdateModule
    db: MemoryDatabase
    bge: BGEEmbeddingClient
    summary: RollingSummaryManager
    monitor: PerformanceMonitor
    llm_client: any  # LLM client for processing
    supabase_mcp: RealSupabaseMCP  # Real Supabase MCP integration
    session_manager: SessionManager  # Session management

@asynccontextmanager
async def memory_system_lifespan(server: FastMCP) -> AsyncIterator[MemorySystemContext]:
    """
    Manages the Spark memory system lifecycle with two-phase architecture.
    
    Args:
        server: The FastMCP server instance
        
    Yields:
        MemorySystemContext: The context containing all memory components
    """
    logger.info("Initializing Spark Memory System with two-phase architecture")
    
    try:
        # Initialize LLM cache service first
        llm_cache = await initialize_llm_cache()
        logger.info("Initialized LLM cache service")
        
        # Initialize BGE embedding client
        bge_client = BGEEmbeddingClient()
        logger.info("Initialized BGE embedding client")
        
        # Initialize real Supabase MCP integration
        supabase_mcp = RealSupabaseMCP()
        logger.info("Initialized real Supabase MCP integration")

        # Get LLM client first (needed for graph service)
        try:
            llm_client = get_spark_memory_client()
            logger.info(f"Initialized LLM client: {os.getenv('LLM_PROVIDER')} - {os.getenv('LLM_CHOICE')}")
        except ValueError as e:
            logger.error(f"LLM client initialization failed: {e}")
            logger.error("Please check your environment variables: LLM_PROVIDER, LLM_CHOICE, LLM_API_KEY")
            raise RuntimeError(f"Server startup failed due to LLM configuration: {e}")

        # Initialize graph service
        graph_service = None
        try:
            graph_service = GraphMemoryService(llm_client)
            logger.info("Initialized graph memory service")
        except Exception as e:
            logger.warning(f"Graph service initialization failed, continuing without graph features: {e}")

        # Initialize database (with enhanced database service and graph integration)
        db_url = os.getenv('DATABASE_URL')
        logger.info(f"=== DATABASE CONFIGURATION ===")
        logger.info(f"DATABASE_URL exists: {bool(db_url)}")
        logger.info(f"Using conservative pool config: min=1, max=2 (to prevent connection exhaustion)")

        db_client = MemoryDatabase(
            supabase_mcp=supabase_mcp,
            database_url=db_url,
            min_connections=1,
            max_connections=2,
            graph_service=graph_service,
            llm_client=llm_client
        )
        await db_client.initialize()
        logger.info("Initialized memory database with enhanced service and graph integration")

        # Initialize rolling summary manager
        summary_manager = RollingSummaryManager(llm_client, db_client)
        logger.info("Initialized rolling summary manager")
        
        # Initialize performance monitor
        performance_monitor = PerformanceMonitor()
        logger.info("Initialized performance monitor")
        
        # Initialize session manager
        session_manager = SessionManager(db_client.db_service)
        await session_manager.start_cleanup_task()
        logger.info("Initialized session manager with cleanup task")

        # Initialize two-phase pipeline modules with real MCP
        extraction_module = MemoryExtractionModule(llm_client, bge_client)
        update_module = MemoryUpdateModule(llm_client, db_client, bge_client, supabase_mcp)
        logger.info("Initialized two-phase memory pipeline with real MCP")

        # Test BGE server connection
        if not await bge_client.health_check():
            logger.warning("BGE server health check failed - embeddings may not work")

        # Create context
        context = MemorySystemContext(
            extraction=extraction_module,
            update=update_module,
            db=db_client,
            bge=bge_client,
            summary=summary_manager,
            monitor=performance_monitor,
            llm_client=llm_client,
            supabase_mcp=supabase_mcp,
            session_manager=session_manager
        )
        
        logger.info("Spark Memory System initialized successfully")

        # Mark components as ready - MCP protocol initialization happens after this
        readiness_manager.set_components_ready()
        logger.info("Components initialization complete, waiting for MCP protocol")

        yield context
        
    except Exception as e:
        logger.error(f"Failed to initialize memory system: {e}")
        raise
    finally:
        # Cleanup resources
        try:
            await bge_client.close()
            await db_client.close()

            # Stop session manager cleanup task
            if 'session_manager' in locals():
                await session_manager.stop_cleanup_task()

            # Close LLM cache service
            from llm_cache_service import get_llm_cache_service
            cache_service = get_llm_cache_service()
            await cache_service.close()

            logger.info("Memory system cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

# Enhanced lifespan manager to track MCP protocol initialization
@asynccontextmanager
async def enhanced_memory_system_lifespan(server: FastMCP) -> AsyncIterator[MemorySystemContext]:
    """
    Enhanced lifespan manager that properly tracks MCP protocol initialization.
    """
    logger.info("Starting enhanced memory system lifespan")
    
    # Delegate to original memory system lifespan
    async with memory_system_lifespan(server) as context:
        # Mark MCP protocol as ready immediately after components are ready
        readiness_manager.set_mcp_protocol_ready()
        readiness_manager.set_service_available()
        logger.info("MCP protocol initialization complete - service now fully available")
        
        # Small delay to ensure all async tasks are properly started
        await asyncio.sleep(0.5)
        
        yield context

# Initialize FastMCP server with enhanced memory system
mcp = FastMCP(
    "spark-mcp",
    description="Spark - Self-evolved memory MCP server with two-phase architecture and BGE embeddings",
    lifespan=enhanced_memory_system_lifespan,
    host=config.server.DEFAULT_HOST,
    port=config.server.DEFAULT_PORT
)

# Note: Custom HTTP routes not supported in this FastMCP version
# Using MCP tool-based health check instead

# OpenMemory MCP Interface Implementation

async def check_server_readiness_with_retry(max_attempts: int = None) -> bool:
    """
    Check if server is ready with retry logic for race conditions.
    
    Args:
        max_attempts: Maximum number of retry attempts (uses config default if None)
        
    Returns:
        True if server is ready
        
    Raises:
        RuntimeError: If server is not ready after all attempts
    """
    if max_attempts is None:
        max_attempts = config.performance.MAX_RETRIES
        
    for attempt in range(max_attempts):
        if readiness_manager.is_ready():
            return True
        
        if not readiness_manager.should_retry_request(attempt):
            break
        
        delay = readiness_manager.get_retry_delay(attempt)
        logger.info(f"Server not ready, retrying in {delay}s (attempt {attempt + 1}/{max_attempts})")
        await asyncio.sleep(delay)
    
    # Provide detailed status for debugging
    status = readiness_manager.get_status()
    error_msg = f"Server initialization incomplete after {max_attempts} attempts. Status: {status}"
    
    if not status["components_ready"]:
        error_msg += " - Components not initialized"
    elif not status["mcp_protocol_ready"]:
        error_msg += " - MCP protocol not ready"
    else:
        error_msg += " - Service not available"
    
    raise RuntimeError(error_msg)

def check_server_readiness() -> bool:
    """Legacy synchronous readiness check for immediate use."""
    if not readiness_manager.is_ready():
        status = readiness_manager.get_status()
        if not status["components_ready"]:
            raise RuntimeError("Server components are still initializing. Please wait and try again.")
        elif not status["mcp_protocol_ready"]:
            raise RuntimeError("MCP protocol is still initializing. Please wait and try again.")
        else:
            raise RuntimeError("Service is not yet available. Please wait and try again.")
    return True

def handle_race_condition_error(func_name: str, error: Exception) -> dict:
    """
    Handle race condition errors with appropriate error responses.
    
    Args:
        func_name: Name of the function that encountered the error
        error: The exception that occurred
        
    Returns:
        Standardized error response dictionary
    """
    error_msg = str(error)
    
    # Check for common race condition patterns
    if "Received request before initialization" in error_msg:
        return {
            "success": False,
            "error": "server_initializing",
            "message": "Server is still starting up. Please wait a moment and try again.",
            "retry_after": 5,
            "function": func_name
        }
    elif "BrokenResourceError" in error_msg or "ConnectionError" in error_msg:
        return {
            "success": False,
            "error": "connection_error", 
            "message": "Temporary connection issue. Please retry in a few seconds.",
            "retry_after": 3,
            "function": func_name
        }
    elif "asyncio" in error_msg and "coroutine" in error_msg:
        return {
            "success": False,
            "error": "async_error",
            "message": "Internal async operation error. Please retry.",
            "retry_after": 2,
            "function": func_name
        }
    else:
        return {
            "success": False,
            "error": "unknown_error",
            "message": f"An error occurred: {error_msg}",
            "function": func_name
        }

@mcp.tool()
async def add_memories(ctx: Context, text: str, session_id: Optional[str] = None) -> str:
    """
    Add memories using the two-phase pipeline (extraction → update).

    This implements the evidence-based two-phase architecture from Mem0 research
    with comprehensive error handling and performance tracking.

    Args:
        ctx: The MCP server provided context
        text: The content to process through the memory pipeline
        session_id: Optional session identifier for session-based tracking

    Returns:
        JSON string with processing results
    """
    user_id = get_server_user_id()
    logger.info(f"add_memories called for server user_id: {user_id}")
    
    try:
        # Check server readiness with retry logic
        await check_server_readiness_with_retry()
        
        # Access memory system context safely
        try:
            memory_system = await get_memory_system_context(ctx, "add_memories")
        except Exception as context_error:
            error_response = create_context_access_error_response(
                context_error, user_id, "add_memories"
            )
            return json.dumps(error_response, indent=2)
        
        # Execute memory pipeline with performance tracking
        result = await memory_system.monitor.track_operation(
            "add_memories", 
            process_memory_pipeline,
            memory_system, text, user_id
        )
        
        return json.dumps(result, indent=2)
        
    except Exception as e:
        logger.error(f"Error in add_memories: {e}")
        error_response = create_memory_operation_error_response(e, user_id, "add_memories")
        return json.dumps(error_response, indent=2)

@mcp.tool()
async def search_memory(ctx: Context, query: str, limit: int = 5, filters: Optional[str] = None, session_id: Optional[str] = None) -> str:
    """
    Search memories using enhanced BGE vector similarity with optional filters.

    Phase 4 Enhancement: Supports metadata filtering, query expansion, and recency boosting.

    Args:
        ctx: The MCP server provided context
        query: Search query string
        limit: Maximum number of results to return
        filters: Optional filter string (e.g., "last:7d,context:work,expand:true")
            - last:Nd/Nw/Nm - Filter by time period (days/weeks/months)
            - context:value - Filter by specific context
            - topics:topic1;topic2 - Filter by topics (semicolon-separated)
            - recency:true/false - Enable/disable recency boosting (default: true)
            - expand:true/false - Enable/disable query expansion (default: true)
        session_id: Optional session identifier to filter results to specific session
            - graph:true/false - Enable/disable graph enhancement (default: true)
        
    Returns:
        JSON string with enhanced search results
    """
    try:
        # Get server-controlled user ID (no client override)
        user_id = get_server_user_id()
        # Check readiness with retry logic for race conditions
        await check_server_readiness_with_retry()

        # Access memory system context with error handling
        try:
            memory_system = ctx.request_context.lifespan_context
        except Exception as context_error:
            logger.error(f"Context access failed in search_memory: {context_error}")
            error_response = {
                "success": False,
                "error": "context_access_error",
                "message": f"Context access failed: {str(context_error)}",
                "function": "search_memory",
                "query": query,
                "results": [],
                "user_id": user_id
            }
            return json.dumps(error_response, indent=2)
        
        # Parse filters if provided
        parsed_filters = parse_search_filters(filters) if filters else None
        
        # Check if enhanced features are available
        config = get_config()
        enhanced_available = (
            hasattr(memory_system, 'llm') and 
            hasattr(memory_system, 'summary') and
            config.database.ENABLE_ENHANCED_SEARCH
        )
        
        async def perform_search():
            # Perform search with enhanced features if available
            result = await search_memory_enhanced(
                memory_system=memory_system,
                query=query,
                user_id=user_id,
                limit=limit,
                filters=parsed_filters,
                enable_enhanced_features=enhanced_available
            )
            
            # Format results for backward compatibility
            if result.get("success", False) and "results" in result:
                # Extract just content for simple format if not using enhanced features
                if not result.get("enhanced_features_used", False):
                    result["results"] = [r.get("content", "") for r in result["results"]]
            
            return result
        
        # Execute with performance tracking
        result = await memory_system.monitor.track_operation(
            "search_memory", perform_search
        )
        
        return json.dumps(result, indent=2)
        
    except Exception as e:
        logger.error(f"Error in search_memory: {e}")
        error_response = handle_race_condition_error("search_memory", e)
        error_response.update({
            "query": query,
            "results": [],
            "user_id": user_id
        })
        return json.dumps(error_response, indent=2)

@mcp.tool()
async def search_by_entity(ctx: Context, entity_name: str, limit: int = 10) -> str:
    """
    Search memories by entity name using graph relationships.
    
    Phase 4 Enhancement: Entity-based search through Neo4j graph.
    
    Args:
        ctx: The MCP server provided context
        entity_name: Name of the entity to search for (person, project, technology, etc.)
        limit: Maximum number of results to return
        
    Returns:
        JSON string with memories related to the entity
    """
    try:
        # Get server-controlled user ID (no client override)
        user_id = get_server_user_id()
        # Check readiness with retry logic for race conditions
        await check_server_readiness_with_retry()

        # Access memory system context with error handling
        try:
            memory_system = ctx.request_context.lifespan_context
        except Exception as context_error:
            logger.error(f"Context access failed in search_by_entity: {context_error}")
            error_response = {
                "success": False,
                "error": "context_access_error",
                "message": f"Context access failed: {str(context_error)}",
                "function": "search_by_entity",
                "entity": entity_name,
                "results": [],
                "user_id": user_id
            }
            return json.dumps(error_response, indent=2)
        
        async def perform_entity_search():
            # Check if graph service is available
            if not hasattr(memory_system.db, 'graph_service') or not memory_system.db.graph_service:
                return {
                    "success": False,
                    "error": "graph_service_unavailable",
                    "message": "Graph service is not available for entity search",
                    "entity": entity_name,
                    "results": [],
                    "user_id": user_id
                }
            
            # Perform entity search
            result = await search_by_entity_handler(
                memory_system=memory_system,
                entity_name=entity_name,
                user_id=user_id,
                limit=limit
            )
            
            return result
        
        # Execute with performance tracking
        result = await memory_system.monitor.track_operation(
            "search_by_entity", perform_entity_search
        )
        
        return json.dumps(result, indent=2)
        
    except Exception as e:
        logger.error(f"Error in search_by_entity: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "entity": entity_name,
            "results": [],
            "user_id": user_id
        }, indent=2)

@mcp.tool()
async def list_memories(ctx: Context, limit: int = 50, session_id: Optional[str] = None) -> str:
    """
    List all memories for a user, optionally filtered by session.

    Args:
        ctx: The MCP server provided context
        limit: Maximum number of memories to return
        session_id: Optional session identifier to filter results

    Returns:
        JSON string with all user memories
    """
    try:
        # Get server-controlled user ID (no client override)
        user_id = get_server_user_id()
        # Check readiness with retry logic for race conditions
        await check_server_readiness_with_retry()

        # Access memory system context with error handling
        try:
            memory_system = ctx.request_context.lifespan_context
        except Exception as context_error:
            logger.error(f"Context access failed in list_memories: {context_error}")
            error_response = {
                "success": False,
                "error": "context_access_error",
                "message": f"Context access failed: {str(context_error)}",
                "function": "list_memories",
                "memories": [],
                "user_id": user_id,
                "count": 0,
                "limit": limit
            }
            return json.dumps(error_response, indent=2)
        
        async def get_memories():
            # Execute via real Supabase MCP
            results = await memory_system.supabase_mcp.get_all_memories(
                user_id, limit=limit, offset=0
            )
            
            return {
                "success": True,
                "memories": [r.get("content", "") for r in results],
                "user_id": user_id,
                "count": len(results),
                "limit": limit
            }
        
        # Execute with performance tracking
        result = await memory_system.monitor.track_operation(
            "list_memories", get_memories
        )
        
        return json.dumps(result, indent=2)
        
    except Exception as e:
        logger.error(f"Error in list_memories: {e}")
        error_response = handle_race_condition_error("list_memories", e)
        error_response.update({
            "memories": [],
            "user_id": user_id
        })
        return json.dumps(error_response, indent=2)

@mcp.tool()
async def delete_all_memories(ctx: Context) -> str:
    """
    Delete all memories for a user.
    
    Args:
        ctx: The MCP server provided context
        
    Returns:
        JSON string with deletion results
    """
    try:
        # Get server-controlled user ID (no client override)
        user_id = get_server_user_id()
        # Check readiness with retry logic for race conditions
        await check_server_readiness_with_retry()

        # Access memory system context with error handling
        try:
            memory_system = ctx.request_context.lifespan_context
        except Exception as context_error:
            logger.error(f"Context access failed in delete_all_memories: {context_error}")
            error_response = {
                "success": False,
                "error": "context_access_error",
                "message": f"Context access failed: {str(context_error)}",
                "function": "delete_all_memories",
                "user_id": user_id,
                "deleted_count": 0
            }
            return json.dumps(error_response, indent=2)
        
        async def delete_memories():
            # Execute via real Supabase MCP
            deleted_count = await memory_system.supabase_mcp.delete_all_memories(user_id)
            
            # Also clear conversation summary
            await memory_system.supabase_mcp.execute_sql(
                "DELETE FROM conversation_summaries WHERE user_id = $1", [user_id]
            )
            
            # Clear summary cache
            memory_system.summary.clear_cache(user_id)
            
            return {
                "success": True,
                "deleted_count": deleted_count,
                "user_id": user_id
            }
        
        # Execute with performance tracking
        result = await memory_system.monitor.track_operation(
            "delete_all_memories", delete_memories
        )
        
        return json.dumps(result, indent=2)
        
    except Exception as e:
        logger.error(f"Error in delete_all_memories: {e}")
        error_response = handle_race_condition_error("delete_all_memories", e)
        error_response["user_id"] = user_id
        return json.dumps(error_response, indent=2)

@mcp.tool()
async def get_bge_health_status(ctx: Context) -> str:
    """
    Get comprehensive BGE embedding health status and performance metrics.
    
    Args:
        ctx: The MCP server provided context
        
    Returns:
        JSON string with BGE health status and metrics
    """
    try:
        # Check readiness
        await check_server_readiness_with_retry()
        
        # Access memory system
        memory_system = ctx.request_context.lifespan_context
        
        # Get comprehensive health check
        health_status = await memory_system.bge.health_check()
        
        # Add performance metrics
        performance_metrics = memory_system.bge.get_performance_metrics()
        health_status["performance_metrics"] = performance_metrics
        
        # Add system status
        health_status["server_status"] = {
            "timestamp": datetime.now().isoformat(),
            "bge_client_type": "enhanced" if hasattr(memory_system.bge, 'direct_manager') else "basic"
        }
        
        return json.dumps(health_status, indent=2)
        
    except Exception as e:
        logger.error(f"Error getting BGE health status: {e}")
        error_response = {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
        return json.dumps(error_response, indent=2)

@mcp.tool() 
async def benchmark_bge_performance(ctx: Context, test_texts: Optional[List[str]] = None) -> str:
    """
    Benchmark BGE embedding performance with test data.
    
    Args:
        ctx: The MCP server provided context
        test_texts: Optional list of test texts (defaults to standard benchmark)
        
    Returns:
        JSON string with benchmark results
    """
    try:
        # Check readiness
        await check_server_readiness_with_retry()
        
        # Access memory system
        memory_system = ctx.request_context.lifespan_context
        
        # Default test texts if none provided
        if not test_texts:
            test_texts = [
                "This is a test sentence for embedding performance.",
                "Natural language processing with transformers is powerful.",
                "Vector similarity search enables semantic memory systems.",
                "BGE embeddings provide high-quality text representations.",
                "Memory systems benefit from efficient embedding generation."
            ]
        
        # Benchmark single embedding
        import time
        start_time = time.time()
        single_embedding = await memory_system.bge.embed_single(test_texts[0])
        single_time = time.time() - start_time
        
        # Benchmark batch embedding
        start_time = time.time()
        batch_embeddings = await memory_system.bge.embed_texts(test_texts)
        batch_time = time.time() - start_time
        
        # Calculate performance metrics
        single_per_text = single_time
        batch_per_text = batch_time / len(test_texts) if test_texts else 0
        speedup_ratio = single_per_text / batch_per_text if batch_per_text > 0 else 0
        
        benchmark_results = {
            "success": True,
            "timestamp": datetime.now().isoformat(),
            "test_size": len(test_texts),
            "single_embedding": {
                "time_seconds": single_time,
                "embedding_dimensions": len(single_embedding) if single_embedding else 0
            },
            "batch_embedding": {
                "total_time_seconds": batch_time,
                "time_per_text_seconds": batch_per_text,
                "embeddings_generated": len(batch_embeddings)
            },
            "performance": {
                "batch_speedup_ratio": speedup_ratio,
                "throughput_texts_per_second": len(test_texts) / batch_time if batch_time > 0 else 0,
                "embedding_method": "direct_gpu" if hasattr(memory_system.bge, 'direct_manager') and memory_system.bge.direct_manager and memory_system.bge.direct_manager.is_available() else "http_service"
            }
        }
        
        return json.dumps(benchmark_results, indent=2)
        
    except Exception as e:
        logger.error(f"Error benchmarking BGE performance: {e}")
        error_response = {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
        return json.dumps(error_response, indent=2)

# Additional tools for monitoring and debugging

@mcp.tool()
async def get_performance_stats(ctx: Context) -> str:
    """
    Get current performance statistics and LOCOMO target comparison.
    
    Args:
        ctx: The MCP server provided context
        
    Returns:
        JSON string with performance metrics
    """
    try:
        # Access memory system context with error handling
        try:
            memory_system = ctx.request_context.lifespan_context
        except Exception as context_error:
            logger.error(f"Context access failed in get_performance_stats: {context_error}")
            error_response = {
                "error": "context_access_error",
                "message": f"Context access failed: {str(context_error)}",
                "function": "get_performance_stats",
                "performance": {
                    "operations_count": 0,
                    "success_rate": 0.0,
                    "avg_response_time": 0.0,
                    "status": "unavailable"
                }
            }
            return json.dumps(error_response, indent=2)

        # Get base performance stats
        stats = memory_system.monitor.get_performance_stats()
        
        # Add cache performance metrics
        from llm_cache_service import get_llm_cache_service
        cache_service = get_llm_cache_service()
        cache_metrics = cache_service.get_metrics()
        
        # Enhance stats with cache information
        stats["cache_performance"] = cache_metrics
        
        return json.dumps(stats, indent=2)

    except Exception as e:
        logger.error(f"Error getting performance stats: {e}")
        return json.dumps({"error": str(e)})

@mcp.tool()
async def get_conversation_summary(ctx: Context) -> str:
    """
    Get current conversation summary for a user.
    
    Args:
        ctx: The MCP server provided context
        
    Returns:
        Current conversation summary
    """
    try:
        # Get server-controlled user ID (no client override)
        user_id = get_server_user_id()
        # Access memory system context with error handling
        try:
            memory_system = ctx.request_context.lifespan_context
        except Exception as context_error:
            logger.error(f"Context access failed in get_conversation_summary: {context_error}")
            error_response = {
                "success": False,
                "error": "context_access_error",
                "message": f"Context access failed: {str(context_error)}",
                "function": "get_conversation_summary",
                "user_id": user_id,
                "summary": "",
                "cache_stats": {}
            }
            return json.dumps(error_response, indent=2)

        summary = await memory_system.summary.get_conversation_summary(user_id)

        return json.dumps({
            "success": True,
            "user_id": user_id,
            "summary": summary,
            "cache_stats": memory_system.summary.get_cache_stats()
        }, indent=2)

    except Exception as e:
        logger.error(f"Error getting conversation summary: {e}")
        return json.dumps({"success": False, "error": str(e), "user_id": user_id})

# Session Management Tools
@mcp.tool()
async def create_session(ctx: Context, session_name: Optional[str] = None, expires_in_hours: int = 24) -> str:
    """
    Create a new session for conversation and memory tracking.

    Args:
        ctx: MCP context
        session_name: Optional human-readable session name
        expires_in_hours: Hours until session expires (default: 24)

    Returns:
        JSON string with session details
    """
    try:
        memory_system = await get_memory_system_context(ctx)
        user_id = get_user_id(ctx)

        session = await memory_system.session_manager.create_session(
            user_id=user_id,
            session_name=session_name,
            expires_in_hours=expires_in_hours
        )

        return json.dumps({
            "success": True,
            "session": session.to_dict(),
            "user_id": user_id
        }, indent=2)

    except Exception as e:
        logger.error(f"Error creating session: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "user_id": get_user_id(ctx)
        }, indent=2)

@mcp.tool()
async def get_active_session(ctx: Context) -> str:
    """
    Get the current active session for the user.

    Args:
        ctx: MCP context

    Returns:
        JSON string with active session details or null if none
    """
    try:
        memory_system = await get_memory_system_context(ctx)
        user_id = get_user_id(ctx)

        session = await memory_system.session_manager.get_active_session(user_id)

        return json.dumps({
            "success": True,
            "session": session.to_dict() if session else None,
            "user_id": user_id
        }, indent=2)

    except Exception as e:
        logger.error(f"Error getting active session: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "user_id": get_user_id(ctx)
        }, indent=2)

@mcp.tool()
async def list_user_sessions(ctx: Context, status: Optional[str] = None, limit: int = 10) -> str:
    """
    List sessions for the user.

    Args:
        ctx: MCP context
        status: Optional status filter ('active', 'ended', 'expired')
        limit: Maximum number of sessions to return

    Returns:
        JSON string with list of sessions
    """
    try:
        memory_system = await get_memory_system_context(ctx)
        user_id = get_user_id(ctx)

        sessions = await memory_system.session_manager.get_user_sessions(
            user_id=user_id,
            status=status,
            limit=limit
        )

        return json.dumps({
            "success": True,
            "sessions": [session.to_dict() for session in sessions],
            "count": len(sessions),
            "user_id": user_id
        }, indent=2)

    except Exception as e:
        logger.error(f"Error listing sessions: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "user_id": get_user_id(ctx)
        }, indent=2)

@mcp.tool()
async def end_session(ctx: Context, session_id: str) -> str:
    """
    End a specific session.

    Args:
        ctx: MCP context
        session_id: Session identifier to end

    Returns:
        JSON string with operation result
    """
    try:
        memory_system = await get_memory_system_context(ctx)
        user_id = get_user_id(ctx)

        success = await memory_system.session_manager.end_session(session_id)

        return json.dumps({
            "success": success,
            "session_id": session_id,
            "user_id": user_id,
            "message": "Session ended successfully" if success else "Session not found or already ended"
        }, indent=2)

    except Exception as e:
        logger.error(f"Error ending session: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "session_id": session_id,
            "user_id": get_user_id(ctx)
        }, indent=2)

@mcp.tool()
async def get_session_stats(ctx: Context, session_id: str) -> str:
    """
    Get statistics for a specific session.

    Args:
        ctx: MCP context
        session_id: Session identifier

    Returns:
        JSON string with session statistics
    """
    try:
        memory_system = await get_memory_system_context(ctx)
        user_id = get_user_id(ctx)

        stats = await memory_system.session_manager.get_session_stats(session_id)

        return json.dumps({
            "success": True,
            "session_id": session_id,
            "stats": stats,
            "user_id": user_id
        }, indent=2)

    except Exception as e:
        logger.error(f"Error getting session stats: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "session_id": session_id,
            "user_id": get_user_id(ctx)
        }, indent=2)

# Confidence Management Tools
@mcp.tool()
async def get_high_confidence_memories(ctx: Context, limit: int = 10, min_confidence: float = 0.7) -> str:
    """
    Get memories with high confidence scores.

    Args:
        ctx: MCP context
        limit: Maximum number of memories to return
        min_confidence: Minimum confidence threshold (default: 0.7)

    Returns:
        JSON string with high-confidence memories
    """
    try:
        memory_system = await get_memory_system_context(ctx)
        user_id = get_user_id(ctx)

        # Query high confidence memories
        sql = """
            SELECT id, content, confidence_score, confidence_factors,
                   created_at, updated_at, session_id
            FROM memories
            WHERE user_id = $1 AND confidence_score >= $2
            ORDER BY confidence_score DESC, created_at DESC
            LIMIT $3
        """

        results = await memory_system.memory_db.execute_sql(sql, [user_id, min_confidence, limit])

        memories = []
        for row in results:
            memories.append({
                'id': str(row['id']),
                'content': row['content'],
                'confidence_score': row['confidence_score'],
                'confidence_factors': row['confidence_factors'],
                'created_at': row['created_at'].isoformat() if row['created_at'] else None,
                'session_id': str(row['session_id']) if row['session_id'] else None
            })

        return json.dumps({
            "success": True,
            "memories": memories,
            "count": len(memories),
            "min_confidence": min_confidence,
            "user_id": user_id
        }, indent=2)

    except Exception as e:
        logger.error(f"Error getting high confidence memories: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "user_id": get_user_id(ctx)
        }, indent=2)

@mcp.tool()
async def update_memory_confidence(ctx: Context, memory_id: str, new_confidence: float, reason: str = "") -> str:
    """
    Update the confidence score of a specific memory.

    Args:
        ctx: MCP context
        memory_id: ID of the memory to update
        new_confidence: New confidence score (0.0-1.0)
        reason: Reason for the confidence update

    Returns:
        JSON string with update result
    """
    try:
        memory_system = await get_memory_system_context(ctx)
        user_id = get_user_id(ctx)

        # Validate confidence range
        if not 0.0 <= new_confidence <= 1.0:
            return json.dumps({
                "success": False,
                "error": "Confidence score must be between 0.0 and 1.0",
                "user_id": user_id
            }, indent=2)

        # Update confidence using database function
        sql = "SELECT update_memory_confidence($1::uuid, $2, NULL, $3)"
        results = await memory_system.memory_db.execute_sql(sql, [memory_id, new_confidence, reason])

        success = results[0]['update_memory_confidence'] if results else False

        return json.dumps({
            "success": success,
            "memory_id": memory_id,
            "new_confidence": new_confidence,
            "reason": reason,
            "user_id": user_id
        }, indent=2)

    except Exception as e:
        logger.error(f"Error updating memory confidence: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "memory_id": memory_id,
            "user_id": get_user_id(ctx)
        }, indent=2)

@mcp.tool()
async def get_confidence_statistics(ctx: Context) -> str:
    """
    Get confidence statistics for the user's memories.

    Args:
        ctx: MCP context

    Returns:
        JSON string with confidence statistics
    """
    try:
        memory_system = await get_memory_system_context(ctx)
        user_id = get_user_id(ctx)

        # Get statistics from the view
        sql = "SELECT * FROM user_confidence_stats WHERE user_id = $1"
        results = await memory_system.memory_db.execute_sql(sql, [user_id])

        if results:
            stats = dict(results[0])
            # Convert Decimal to float for JSON serialization
            for key, value in stats.items():
                if hasattr(value, '__float__'):
                    stats[key] = float(value)
        else:
            stats = {
                "user_id": user_id,
                "total_memories": 0,
                "avg_confidence": 0.0,
                "min_confidence": 0.0,
                "max_confidence": 0.0,
                "high_confidence_count": 0,
                "medium_confidence_count": 0,
                "low_confidence_count": 0
            }

        return json.dumps({
            "success": True,
            "statistics": stats,
            "user_id": user_id
        }, indent=2)

    except Exception as e:
        logger.error(f"Error getting confidence statistics: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "user_id": get_user_id(ctx)
        }, indent=2)

# Enhanced Rolling Summary Tools (Phase 2 Feature 3/3)

@mcp.tool()
async def get_summary_quality_stats(ctx: Context) -> str:
    """
    Get summary quality statistics for the user.

    Args:
        ctx: MCP context

    Returns:
        JSON string with summary quality statistics
    """
    try:
        memory_system = await get_memory_system_context(ctx)
        user_id = get_user_id(ctx)

        quality_score = await memory_system.rolling_summary_manager.get_summary_quality_score(user_id)

        return json.dumps({
            "success": True,
            "user_id": user_id,
            "current_quality_score": round(quality_score, 3),
            "quality_level": "high" if quality_score >= 0.7 else "medium" if quality_score >= 0.4 else "low",
            "phase2_feature": "Enhanced Rolling Summary Features",
            "status": "active"
        }, indent=2)

    except Exception as e:
        logger.error(f"Error getting summary quality stats: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "user_id": get_user_id(ctx)
        }, indent=2)

@mcp.tool()
async def get_cross_session_topics(ctx: Context) -> str:
    """
    Get topics that span across multiple sessions for the user.

    Args:
        ctx: MCP context

    Returns:
        JSON string with cross-session topics
    """
    try:
        memory_system = await get_memory_system_context(ctx)
        user_id = get_user_id(ctx)

        topics = await memory_system.rolling_summary_manager.get_cross_session_topics(user_id)

        topic_data = []
        for topic in topics:
            topic_data.append({
                "name": topic.name,
                "keywords": topic.keywords,
                "importance_score": round(topic.importance_score, 3),
                "session_count": len(topic.session_ids or []),
                "status": topic.status,
                "mention_count": topic.mention_count
            })

        return json.dumps({
            "success": True,
            "user_id": user_id,
            "cross_session_topics": topic_data,
            "total_topics": len(topic_data),
            "phase2_feature": "Topic Continuity Management"
        }, indent=2)

    except Exception as e:
        logger.error(f"Error getting cross-session topics: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "user_id": get_user_id(ctx)
        }, indent=2)

@mcp.tool()
async def get_temporal_evolution(ctx: Context) -> str:
    """
    Get temporal evolution analysis for the user's conversation context.

    Args:
        ctx: MCP context

    Returns:
        JSON string with temporal evolution data
    """
    try:
        memory_system = await get_memory_system_context(ctx)
        user_id = get_user_id(ctx)

        evolution_data = await memory_system.rolling_summary_manager.get_temporal_evolution(user_id)

        return json.dumps({
            "success": True,
            "user_id": user_id,
            "temporal_evolution": evolution_data,
            "phase2_feature": "Temporal Context Tracking"
        }, indent=2)

    except Exception as e:
        logger.error(f"Error getting temporal evolution: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "user_id": get_user_id(ctx)
        }, indent=2)

@mcp.tool()
async def update_summary_with_phase2_features(
    ctx: Context,
    messages: str,
    session_id: str = None
) -> str:
    """
    Update conversation summary using Phase 2 enhanced features.

    Args:
        ctx: MCP context
        messages: New conversation messages (JSON array or single message)
        session_id: Optional session identifier

    Returns:
        JSON string with update status
    """
    try:
        memory_system = await get_memory_system_context(ctx)
        user_id = get_user_id(ctx)

        # Parse messages
        if messages.startswith('['):
            message_list = json.loads(messages)
        else:
            message_list = [messages]

        # Update summary with Phase 2 features
        await memory_system.rolling_summary_manager.update_summary_with_phase2_features(
            user_id, message_list, session_id
        )

        # Get updated quality score
        quality_score = await memory_system.rolling_summary_manager.get_summary_quality_score(user_id)

        return json.dumps({
            "success": True,
            "user_id": user_id,
            "session_id": session_id,
            "messages_processed": len(message_list),
            "updated_quality_score": round(quality_score, 3),
            "phase2_features_applied": [
                "Temporal Context Tracking",
                "Topic Continuity Management",
                "Summary Quality Assessment"
            ],
            "status": "success"
        }, indent=2)

    except Exception as e:
        logger.error(f"Error updating summary with Phase 2 features: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "user_id": get_user_id(ctx)
        }, indent=2)

# Import cache performance tools
from cache_performance_tool import get_cache_performance_report, invalidate_llm_cache, cache_warm_common_patterns

# Cache performance tools
@mcp.tool()
async def get_cache_performance_report(ctx: Context) -> str:
    """
    Get comprehensive cache performance report with optimization insights.
    
    Args:
        ctx: The MCP server provided context
        
    Returns:
        JSON string with detailed cache performance analysis
    """
    from cache_performance_tool import get_cache_performance_report as get_report
    return await get_report(ctx)

@mcp.tool()
async def invalidate_llm_cache(ctx: Context, cache_type: str = None) -> str:
    """
    Invalidate LLM cache entries for testing and optimization.
    
    Args:
        ctx: The MCP server provided context
        cache_type: Optional cache type to invalidate (extraction, decision, summary)
        
    Returns:
        JSON string with invalidation results
    """
    from cache_performance_tool import invalidate_llm_cache as invalidate_cache
    return await invalidate_cache(ctx, cache_type)

@mcp.tool()
async def cache_warm_common_patterns(ctx: Context) -> str:
    """
    Warm cache with common memory extraction and decision patterns.
    
    Args:
        ctx: The MCP server provided context
        
    Returns:
        JSON string with cache warming results
    """
    from cache_performance_tool import cache_warm_common_patterns as warm_cache
    return await warm_cache(ctx)

# Health check tool for monitoring (used by Docker health check via MCP tools)
@mcp.tool()
async def health_check(ctx: Context) -> str:
    """
    Health check for Docker container monitoring.
    
    Returns detailed server readiness status including initialization state,
    component health, and system metrics.

    Args:
        ctx: The MCP server provided context
        
    Returns:
        JSON string indicating comprehensive server health status
    """
    try:
        # Get detailed readiness status
        readiness_status = readiness_manager.get_status()
        
        # Determine overall health status
        if readiness_status["ready"]:
            overall_status = "healthy"
        elif readiness_status["components_ready"] and not readiness_status["mcp_protocol_ready"]:
            overall_status = "initializing_mcp"
        elif readiness_status["components_ready"]:
            overall_status = "components_ready"
        else:
            overall_status = "initializing"
        
        health_data = {
            "service": "spark-mcp-server",
            "status": overall_status,
            "readiness": readiness_status,
            "timestamp": datetime.now().isoformat(),
            "initialization_phases": {
                "components": "✅ Ready" if readiness_status["components_ready"] else "⏳ Initializing",
                "mcp_protocol": "✅ Ready" if readiness_status["mcp_protocol_ready"] else "⏳ Initializing", 
                "service": "✅ Available" if readiness_status["service_available"] else "⏳ Waiting"
            }
        }
        
        # If server is ready, include component health checks
        if readiness_status["ready"]:
            try:
                memory_system = ctx.request_context.lifespan_context
                
                # Test BGE server connectivity
                bge_healthy = await memory_system.bge.health_check()
                
                # Get performance stats if available
                perf_stats = memory_system.monitor.get_performance_stats()
                
                health_data.update({
                    "components": {
                        "bge_server": "healthy" if bge_healthy else "degraded",
                        "memory_database": "healthy",  # Assume healthy if we got this far
                        "llm_client": "healthy",
                        "performance_monitor": "healthy"
                    },
                    "performance": {
                        "operations_count": perf_stats.get("performance_metrics", {}).get("total_operations", 0),
                        "success_rate": perf_stats.get("performance_metrics", {}).get("success_rate", 0),
                        "uptime_seconds": perf_stats.get("session_info", {}).get("uptime_seconds", 0)
                    }
                })
                
                # Mark as degraded if BGE server is unhealthy
                if not bge_healthy:
                    health_data["status"] = "degraded"
                    health_data["warnings"] = ["BGE embedding server is not responding"]
                    
            except Exception as component_error:
                health_data.update({
                    "status": "degraded",
                    "components_error": str(component_error),
                    "message": "Server is ready but some components may be degraded"
                })
        else:
            health_data["message"] = "Server is still initializing, please wait"
            
        return json.dumps(health_data, indent=2)
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return json.dumps({
            "service": "spark-mcp-server",
            "status": "unhealthy", 
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        })

async def main():
    """Main entry point with configuration-driven startup."""
    transport = config.server.DEFAULT_TRANSPORT

    logger.info(f"Starting Spark MCP server with {transport} transport")
    logger.info(f"Server: {config.server.DEFAULT_HOST}:{config.server.DEFAULT_PORT}")
    logger.info("Features: Two-phase pipeline, BGE embeddings, PostgreSQL+pgvector")
    logger.info(f"Environment: {'Production' if config.is_production() else 'Development'}")

    if transport == 'sse':
        await mcp.run_sse_async()
    else:
        await mcp.run_stdio_async()

if __name__ == "__main__":
    asyncio.run(main())
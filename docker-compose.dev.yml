version: '3.8'

services:
  # ==========================================
  # Spark Memory MCP Server - Development Mode
  # ==========================================
  spark-mcp-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: spark-mcp-dev
    ports:
      - "8050:8050"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    environment:
      # Development mode
      ENVIRONMENT: development
      PYTHONUNBUFFERED: 1
      
      # MCP Server Configuration
      TRANSPORT: sse
      HOST: 0.0.0.0
      PORT: 8050
      
      # LLM Configuration (from .env)
      LLM_PROVIDER: ${LLM_PROVIDER:-openrouter}
      LLM_API_KEY: ${LLM_API_KEY}
      LLM_CHOICE: ${LLM_CHOICE:-google/gemini-2.5-flash-lite}
      LLM_BASE_URL: ${LLM_BASE_URL:-https://openrouter.ai/api/v1}
      
      # BGE Embedding Server - Points to actual server on Ubuntu host
      BGE_SERVER_URL: ${BGE_SERVER_URL:-http://************:8080}
      
      # Enhanced BGE Configuration
      USE_DIRECT_EMBEDDING: ${USE_DIRECT_EMBEDDING:-false}
      EMBEDDING_MODEL_NAME: ${EMBEDDING_MODEL_NAME:-BAAI/bge-base-en-v1.5}
      EMBEDDING_BATCH_SIZE: ${EMBEDDING_BATCH_SIZE:-32}
      EMBEDDING_DEVICE: ${EMBEDDING_DEVICE:-auto}
      BGE_TIMEOUT: ${BGE_TIMEOUT:-30}
      
      # Database Configuration
      DATABASE_URL: ${DATABASE_URL:-******************************************************/spark_memory}

      # Neo4j Configuration
      NEO4J_URI: ${NEO4J_URI:-bolt://host.docker.internal:7687}
      NEO4J_USER: ${NEO4J_USER:-neo4j}
      NEO4J_PASSWORD: ${NEO4J_PASSWORD}

      # Redis Configuration for LLM Caching
      REDIS_URL: ${REDIS_URL:-redis://redis:6379/0}
      
      # Logging
      UVICORN_LOG_LEVEL: debug
      LOG_LEVEL: DEBUG
    networks:
      - spark-dev-network
    volumes:
      # Hot reload - mount source code
      - ./src:/app/src:ro
      - ./tests:/app/tests:ro
      - ./pyproject.toml:/app/pyproject.toml:ro
      - ./logs:/app/logs
      # Exclude Python cache
      - /app/src/__pycache__
      - /app/.venv
    restart: unless-stopped
    command: ["uv", "run", "python", "src/main_new.py"]
    depends_on:
      postgres-dev:
        condition: service_healthy
      redis:
        condition: service_healthy

  # ==========================================
  # PostgreSQL with pgvector - Development
  # ==========================================
  postgres-dev:
    image: pgvector/pgvector:pg16
    container_name: spark-postgres-dev
    environment:
      POSTGRES_DB: spark_memory
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: spark_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5433:5432"  # Different port to avoid conflicts
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
    networks:
      - spark-dev-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d spark_memory"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    command: >
      postgres 
      -c shared_preload_libraries=vector
      -c max_connections=200
      -c shared_buffers=256MB
      -c log_statement=all
      -c log_duration=on

  # ==========================================
  # Test Database - Isolated for testing
  # ==========================================
  postgres-test:
    image: pgvector/pgvector:pg16
    container_name: spark-postgres-test
    environment:
      POSTGRES_DB: spark_memory_test
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: spark_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5434:5432"
    volumes:
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
    networks:
      - spark-dev-network
    restart: unless-stopped
    profiles:
      - test
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d spark_memory_test"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: >
      postgres 
      -c shared_preload_libraries=vector
      -c max_connections=100
      -c shared_buffers=128MB

  # ==========================================
  # pgAdmin for Database Management
  # ==========================================
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: spark-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: "False"
    ports:
      - "5050:80"
    networks:
      - spark-dev-network
    restart: unless-stopped
    profiles:
      - tools
    volumes:
      - pgadmin_data:/var/lib/pgadmin

  # ==========================================
  # Redis for LLM Response Caching
  # ==========================================
  redis:
    image: redis:7-alpine
    container_name: spark-redis
    ports:
      - "6379:6379"
    command: redis-server --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - spark-dev-network
    restart: unless-stopped

  # ==========================================
  # BGE Mock Server for Development
  # ==========================================
  bge-mock-dev:
    build:
      context: ./tests/mocks
      dockerfile: Dockerfile.bge-mock
    container_name: spark-bge-mock-dev
    ports:
      - "8081:8080"
    networks:
      - spark-dev-network
    restart: unless-stopped
    profiles:
      - mock
    environment:
      LOG_LEVEL: debug

  # ==========================================
  # Test Runner Container
  # ==========================================
  test-runner:
    build:
      context: .
      dockerfile: Dockerfile.test
    container_name: spark-test-runner
    environment:
      DATABASE_URL: *******************************************************/spark_memory_test
      BGE_SERVER_URL: ${BGE_SERVER_URL:-http://************:8080}
      PYTHONPATH: /app
    networks:
      - spark-dev-network
    volumes:
      - ./src:/app/src:ro
      - ./tests:/app/tests:ro
      - ./pyproject.toml:/app/pyproject.toml:ro
      - ./pytest.ini:/app/pytest.ini:ro
      - ./coverage:/app/coverage
    profiles:
      - test
    command: ["pytest", "-v", "--cov=src", "--cov-report=html:coverage/html"]

networks:
  spark-dev-network:
    driver: bridge
    name: spark-dev-network

volumes:
  postgres_dev_data:
    name: spark_postgres_dev_data
  pgadmin_data:
    name: spark_pgadmin_data
  redis_data:
    driver: local

#!/bin/bash
# Start Spark Memory MCP Server for aung-dev user

# Navigate to project directory
cd "$(dirname "$0")/.."

# Create user-specific environment file
cat > .env.user <<EOF
# User-specific configuration for aung-dev
MCP_USER_ID=aung-dev
PORT=8050
NEO4J_NAMESPACE=user_aung_dev

# Include base development settings
$(cat .env.dev)
EOF

echo "🚀 Starting Spark Memory MCP Server for user: aung-dev"
echo "📡 Server will be available at: http://192.168.1.84:8050"
echo "👤 User ID: aung-dev"
echo "🔧 Using environment: .env.user"

# Start with user-specific environment
env $(cat .env.user | xargs) ./dev.sh start
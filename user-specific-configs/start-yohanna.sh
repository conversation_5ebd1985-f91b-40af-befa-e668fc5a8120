#!/bin/bash
# Start Spark Memory MCP Server for yohanna user

# Navigate to project directory
cd "$(dirname "$0")/.."

# Create user-specific environment file
cat > .env.user <<EOF
# User-specific configuration for yohanna
MCP_USER_ID=yohanna
PORT=8051
NEO4J_NAMESPACE=user_yohanna

# Include base development settings
$(cat .env.dev)
EOF

echo "🚀 Starting Spark Memory MCP Server for user: yohanna"
echo "📡 Server will be available at: http://192.168.1.84:8051"
echo "👤 User ID: yohanna"
echo "🔧 Using environment: .env.user"

# Start with user-specific environment and port
env $(cat .env.user | xargs) ./dev.sh start
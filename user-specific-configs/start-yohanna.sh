#!/bin/bash
# Start Spark Memory MCP Server for second team member

cd /home/<USER>/dev/tools/mcp-mem0

# Set user-specific environment
export MCP_USER_ID="yohanna"
export SERVER_PORT=8051

echo "🚀 Starting Spark Memory MCP Server for user: team-member-2"
echo "📡 Server will be available at: http://192.168.1.84:8051"
echo "👤 User ID: $MCP_USER_ID"

# Modify docker-compose to use different port
sed -i 's/8050:8050/8051:8050/g' docker-compose.dev.yml
./dev.sh start
{"timestamp": "2025-08-03T12:56:43.122775", "validation_status": "FAIL", "critical_issues_resolved": true, "environments": {"development": {"container_running": false, "container_status": "", "service_available": false, "log_status": "INFO:__main__:Initialized LLM client: openrouter - google/gemini-2.5-flash-lite\nINFO:graph_memory_service:Initialized GraphMemoryService\nINFO:__main__:Initialized graph memory service\nINFO:__main__:=== DATABASE CONFIGURATION ===\nINFO:__main__:DATABASE_URL exists: True\nINFO:__main__:Using conservative pool config: min=1, max=2 (to prevent connection exhaustion)\nINFO:enhanced_database_service:Initialized EnhancedDatabaseService with pool size 1-2\nINFO:memory_database:Initialized MemoryDatabase with EnhancedDatabaseService (Graph: enabled)\nINFO:enhanced_database_service:=== DATABASE INITIALIZATION ATTEMPT #1 ===\nINFO:enhanced_database_service:Pool configuration: min=1, max=2\nINFO:enhanced_database_service:Database URL: postgresql://postgres:yeGeIy8eFJgfuPpFh1dtAzBXz8YL...\nINFO:enhanced_database_service:PostgreSQL max_connections: 100\nINFO:enhanced_database_service:Reserved for superuser: 3\nINFO:enhanced_database_service:Current active connections: 2\nINFO:enhanced_database_service:Database service initialized successfully\nINFO:graph_memory_service:Neo4j URI: bolt://************:7687\nINFO:graph_memory_service:Neo4j User: neo4j\nINFO:graph_memory_service:Neo4j Password: ***********\nINFO:graph_memory_service:Connection timeout: 30\nINFO:graph_memory_service:Max retry time: 30\nINFO:neo4j.notifications:Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE CONSTRAINT entity_name_unique IF NOT EXISTS FOR (e:Entity) REQUIRE (e.name) IS UNIQUE` has no effect.} {description: `CONSTRAINT entity_name_unique FOR (e:Entity) REQUIRE (e.name) IS UNIQUE` already exists.} {position: None} for query: 'CREATE CONSTRAINT entity_name_unique IF NOT EXISTS FOR (e:Entity) REQUIRE e.name IS UNIQUE'\nINFO:neo4j.notifications:Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE CONSTRAINT memory_id_unique IF NOT EXISTS FOR (e:Memory) REQUIRE (e.id) IS UNIQUE` has no effect.} {description: `CONSTRAINT memory_id_unique FOR (e:Memory) REQUIRE (e.id) IS UNIQUE` already exists.} {position: None} for query: 'CREATE CONSTRAINT memory_id_unique IF NOT EXISTS FOR (m:Memory) REQUIRE m.id IS UNIQUE'\nINFO:neo4j.notifications:Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX entity_type_idx IF NOT EXISTS FOR (e:Entity) ON (e.type)` has no effect.} {description: `RANGE INDEX entity_type_idx FOR (e:Entity) ON (e.type)` already exists.} {position: None} for query: 'CREATE INDEX entity_type_idx IF NOT EXISTS FOR (e:Entity) ON (e.type)'\nINFO:neo4j.notifications:Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX entity_created_idx IF NOT EXISTS FOR (e:Entity) ON (e.created_at)` has no effect.} {description: `RANGE INDEX entity_created_idx FOR (e:Entity) ON (e.created_at)` already exists.} {position: None} for query: 'CREATE INDEX entity_created_idx IF NOT EXISTS FOR (e:Entity) ON (e.created_at)'\nINFO:neo4j.notifications:Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX memory_user_idx IF NOT EXISTS FOR (e:Memory) ON (e.user_id)` has no effect.} {description: `RANGE INDEX memory_user_idx FOR (e:Memory) ON (e.user_id)` already exists.} {position: None} for query: 'CREATE INDEX memory_user_idx IF NOT EXISTS FOR (m:Memory) ON (m.user_id)'\nINFO:neo4j.notifications:Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX memory_created_idx IF NOT EXISTS FOR (e:Memory) ON (e.created_at)` has no effect.} {description: `RANGE INDEX memory_created_idx FOR (e:Memory) ON (e.created_at)` already exists.} {position: None} for query: 'CREATE INDEX memory_created_idx IF NOT EXISTS FOR (m:Memory) ON (m.created_at)'\nINFO:graph_memory_service:Created Neo4j indexes and constraints\nINFO:graph_memory_service:Graph memory service initialized successfully\nINFO:memory_database:Graph memory service initialized successfully\nINFO:__main__:Initialized memory database with enhanced service and graph integration\nINFO:temporal_context_tracker:Initialized TemporalContextTracker\nINFO:topic_continuity_manager:Initialized TopicContinuityManager\nINFO:summary_quality_assessor:Initialized SummaryQualityAssessor\nINFO:rolling_summary:Initialized Enhanced RollingSummaryManager with Phase 2 features (Entity Extraction: False)\nINFO:__main__:Initialized rolling summary manager\nINFO:performance_monitor:Initialized PerformanceMonitor with LOCOMO targets\nINFO:__main__:Initialized performance monitor\nINFO:session_manager:Session cleanup task started\nINFO:__main__:Initialized session manager with cleanup task\nINFO:memory_extraction:Initialized MemoryExtractionModule with LLM caching and confidence scoring\nINFO:memory_update:Initialized MemoryUpdateModule with LLM caching\nINFO:__main__:Initialized two-phase memory pipeline with real MCP\nINFO:httpx:HTTP Request: GET http://************:8080/health \"HTTP/1.1 200 OK\"\nINFO:__main__:Spark Memory System initialized successfully\nINFO:server_readiness:Components marked as ready\nINFO:__main__:Components initialization complete, waiting for MCP protocol\nINFO:server_readiness:MCP protocol marked as ready\nINFO:server_readiness:Service marked as fully available at 2025-08-03 03:26:01.489445\nINFO:__main__:MCP protocol initialization complete - service now fully available\n", "overall_status": "FAILED"}, "production": {"container_running": false, "container_status": "", "service_available": false, "log_status": "Error response from daemon: No such container: spark-mcp-server\n", "overall_status": "FAILED"}}, "components": {"databases": {"postgres_dev": {"running": false, "status": ""}, "postgres_local": {"running": false, "status": ""}}, "neo4j": {"connectivity": false, "status": "FAILED"}}, "summary": "Some components are not operational. Review the detailed status above."}
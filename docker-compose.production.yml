version: '3.8'

networks:
  spark-production-network:
    driver: bridge

services:
  # Production MCP Server for User 1 (aung-dev)
  spark-mcp-user1:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: spark-mcp-production-user1
    networks:
      - spark-production-network
    ports:
      - "8050:8050"  # User 1 port
    env_file:
      - .env.production.user1
    environment:
      - ENVIRONMENT=production
      - MCP_USER_ID=aung-dev
    restart: unless-stopped
    depends_on: []
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8050/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    volumes:
      - ./logs:/app/logs
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Production MCP Server for User 2 (team-member-2)
  spark-mcp-user2:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: spark-mcp-production-user2
    networks:
      - spark-production-network
    ports:
      - "8051:8051"  # User 2 port
    env_file:
      - .env.production.user2
    environment:
      - ENVIRONMENT=production
      - MCP_USER_ID=<EMAIL>
    restart: unless-stopped
    depends_on: []
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8051/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    volumes:
      - ./logs:/app/logs
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"